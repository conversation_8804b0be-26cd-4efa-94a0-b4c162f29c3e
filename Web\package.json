{"name": "admin.net", "type": "module", "version": "2.4.33", "lastBuildTime": "2025.07.19", "description": "Admin.NET 站在巨人肩膀上的 .NET 通用权限开发框架", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "vite", "build": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite build", "lint-fix": "eslint --fix src/", "format": "prettier --write .", "build-api": "cd api_build/ && build.bat", "build-approvalFlow-api": "cd api_build/ && build.bat approvalFlow", "build-dingTalk-api": "cd api_build/ && build.bat dingTalk", "build-goView-api": "cd api_build/ && build.bat goView", "build-all-api": "npm run build-api && npm run build-approvalFlow-api &&  npm run build-dingTalk-api && npm run build-goView-api"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@logicflow/core": "^2.0.16", "@logicflow/extension": "^2.0.21", "@microsoft/signalr": "^8.0.7", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vueuse/core": "^13.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "async-validator": "^4.2.5", "axios": "^1.11.0", "countup.js": "^2.9.0", "cropperjs": "^1.6.2", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.10.4", "ezuikit-js": "8.1.12-beta.1", "js-cookie": "^3.0.5", "js-table2excel": "^1.1.2", "json-editor-vue": "^0.18.1", "jsplumb": "^2.15.6", "lodash-es": "^4.17.21", "md-editor-v3": "^5.8.2", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "mqtt": "^5.13.3", "nprogress": "^0.2.0", "pinia": "^3.0.3", "print-js": "^1.6.0", "push.js": "^1.0.12", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.14.0", "relation-graph": "^2.2.11", "screenfull": "^6.0.2", "sm-crypto-v2": "^1.13.0", "sortablejs": "^1.15.6", "splitpanes": "^4.0.4", "vcrontab-3": "^3.3.22", "vform3-builds": "^3.0.10", "vue": "^3.5.18", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.10", "vue-draggable-plus": "^0.6.0", "vue-grid-layout": "3.0.0-beta1", "vue-i18n": "^11.1.11", "vue-json-pretty": "^2.5.0", "vue-plugin-hiprint": "^0.0.60", "vue-router": "^4.5.1", "vue-signature-pad": "^3.0.2", "vue3-tree-org": "^4.2.2", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@plugin-web-update-notification/vite": "^2.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.5", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/compiler-sfc": "^3.5.18", "code-inspector-plugin": "^0.20.17", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "less": "^4.4.0", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression2": "^2.2.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^10.2.0"}, "pnpm": {"onlyBuiltDependencies": ["@vue-office/docx", "@vue-office/excel", "@vue-office/pdf"], "ignoredBuiltDependencies": ["@parcel/watcher", "core-js", "es5-ext", "esbuild", "json-editor-vue", "vue-demi"], "overrides": {"rollup": "4.43.0"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=18.0.0", "npm": ">= 7.0.0"}, "keywords": ["admin.net", "vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"]}