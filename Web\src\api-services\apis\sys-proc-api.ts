/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultDataSet } from '../models';
import { AdminResultDataTable } from '../models';
import { AdminResultIActionResult } from '../models';
import { BaseProcInput } from '../models';
import { ExportProcByTMPInput } from '../models';
import { ExportProcInput } from '../models';
/**
 * SysProcApi - axios parameter creator
 * @export
 */
export const SysProcApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取存储过程返回数据集-Oracle、达梦参数顺序不能错  Oracle 返回table、table1，其他返回table1、table2。适用于报表、复杂详细页面等 🔖
         * @param {BaseProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysProcCommonDataSetPost: async (body?: BaseProcInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysProc/commonDataSet`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 导出存储过程数据-指定列，没有指定的字段会被隐藏 🔖
         * @param {ExportProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysProcPocExport2Post: async (body?: ExportProcInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysProc/pocExport2`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据模板导出存储过程数据 🔖
         * @param {ExportProcByTMPInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysProcPocExportPost: async (body?: ExportProcByTMPInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysProc/pocExport`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取存储过程返回表-Oracle、达梦参数顺序不能错 🔖
         * @param {BaseProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysProcProcTablePost: async (body?: BaseProcInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysProc/procTable`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysProcApi - functional programming interface
 * @export
 */
export const SysProcApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取存储过程返回数据集-Oracle、达梦参数顺序不能错  Oracle 返回table、table1，其他返回table1、table2。适用于报表、复杂详细页面等 🔖
         * @param {BaseProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcCommonDataSetPost(body?: BaseProcInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDataSet>>> {
            const localVarAxiosArgs = await SysProcApiAxiosParamCreator(configuration).apiSysProcCommonDataSetPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 导出存储过程数据-指定列，没有指定的字段会被隐藏 🔖
         * @param {ExportProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcPocExport2Post(body?: ExportProcInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultIActionResult>>> {
            const localVarAxiosArgs = await SysProcApiAxiosParamCreator(configuration).apiSysProcPocExport2Post(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据模板导出存储过程数据 🔖
         * @param {ExportProcByTMPInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcPocExportPost(body?: ExportProcByTMPInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultIActionResult>>> {
            const localVarAxiosArgs = await SysProcApiAxiosParamCreator(configuration).apiSysProcPocExportPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取存储过程返回表-Oracle、达梦参数顺序不能错 🔖
         * @param {BaseProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcProcTablePost(body?: BaseProcInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDataTable>>> {
            const localVarAxiosArgs = await SysProcApiAxiosParamCreator(configuration).apiSysProcProcTablePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysProcApi - factory interface
 * @export
 */
export const SysProcApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取存储过程返回数据集-Oracle、达梦参数顺序不能错  Oracle 返回table、table1，其他返回table1、table2。适用于报表、复杂详细页面等 🔖
         * @param {BaseProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcCommonDataSetPost(body?: BaseProcInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDataSet>> {
            return SysProcApiFp(configuration).apiSysProcCommonDataSetPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 导出存储过程数据-指定列，没有指定的字段会被隐藏 🔖
         * @param {ExportProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcPocExport2Post(body?: ExportProcInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultIActionResult>> {
            return SysProcApiFp(configuration).apiSysProcPocExport2Post(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据模板导出存储过程数据 🔖
         * @param {ExportProcByTMPInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcPocExportPost(body?: ExportProcByTMPInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultIActionResult>> {
            return SysProcApiFp(configuration).apiSysProcPocExportPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取存储过程返回表-Oracle、达梦参数顺序不能错 🔖
         * @param {BaseProcInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysProcProcTablePost(body?: BaseProcInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDataTable>> {
            return SysProcApiFp(configuration).apiSysProcProcTablePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysProcApi - object-oriented interface
 * @export
 * @class SysProcApi
 * @extends {BaseAPI}
 */
export class SysProcApi extends BaseAPI {
    /**
     * 
     * @summary 获取存储过程返回数据集-Oracle、达梦参数顺序不能错  Oracle 返回table、table1，其他返回table1、table2。适用于报表、复杂详细页面等 🔖
     * @param {BaseProcInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysProcApi
     */
    public async apiSysProcCommonDataSetPost(body?: BaseProcInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDataSet>> {
        return SysProcApiFp(this.configuration).apiSysProcCommonDataSetPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 导出存储过程数据-指定列，没有指定的字段会被隐藏 🔖
     * @param {ExportProcInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysProcApi
     */
    public async apiSysProcPocExport2Post(body?: ExportProcInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultIActionResult>> {
        return SysProcApiFp(this.configuration).apiSysProcPocExport2Post(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据模板导出存储过程数据 🔖
     * @param {ExportProcByTMPInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysProcApi
     */
    public async apiSysProcPocExportPost(body?: ExportProcByTMPInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultIActionResult>> {
        return SysProcApiFp(this.configuration).apiSysProcPocExportPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取存储过程返回表-Oracle、达梦参数顺序不能错 🔖
     * @param {BaseProcInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysProcApi
     */
    public async apiSysProcProcTablePost(body?: BaseProcInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDataTable>> {
        return SysProcApiFp(this.configuration).apiSysProcProcTablePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
