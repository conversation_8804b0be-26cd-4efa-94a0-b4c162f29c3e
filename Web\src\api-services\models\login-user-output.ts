/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * 用户登录信息
 *
 * @export
 * @interface LoginUserOutput
 */
export interface LoginUserOutput {

    /**
     * 用户id
     *
     * @type {number}
     * @memberof LoginUserOutput
     */
    id?: number;

    /**
     * 账号名称
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    account?: string | null;

    /**
     * 真实姓名
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    realName?: string | null;

    /**
     * 电话
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    phone?: string | null;

    /**
     * 身份证
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    idCardNum?: string | null;

    /**
     * 邮箱
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    email?: string | null;

    /**
     * @type {AccountTypeEnum}
     * @memberof LoginUserOutput
     */
    accountType?: AccountTypeEnum;

    /**
     * 头像
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    avatar?: string | null;

    /**
     * 个人简介
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    introduction?: string | null;

    /**
     * 地址
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    address?: string | null;

    /**
     * 电子签名
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    signature?: string | null;

    /**
     * 机构Id
     *
     * @type {number}
     * @memberof LoginUserOutput
     */
    orgId?: number;

    /**
     * 机构名称
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    orgName?: string | null;

    /**
     * 机构类型
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    orgType?: string | null;

    /**
     * 职位名称
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    posName?: string | null;

    /**
     * 按钮权限集合
     *
     * @type {Array<string>}
     * @memberof LoginUserOutput
     */
    buttons?: Array<string> | null;

    /**
     * 角色集合
     *
     * @type {Array<number>}
     * @memberof LoginUserOutput
     */
    roleIds?: Array<number> | null;

    /**
     * 水印文字
     *
     * @type {string}
     * @memberof LoginUserOutput
     */
    watermarkText?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof LoginUserOutput
     */
    tenantId?: number | null;

    /**
     * 当前切换到的租户Id
     *
     * @type {number}
     * @memberof LoginUserOutput
     */
    currentTenantId?: number | null;
}
