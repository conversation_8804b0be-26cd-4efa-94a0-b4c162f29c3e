/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
import { SysOrg } from './sys-org';
 /**
 * 系统机构表
 *
 * @export
 * @interface SysOrg
 */
export interface SysOrg {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof SysOrg
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof SysOrg
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof SysOrg
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof SysOrg
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof SysOrg
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof SysOrg
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof SysOrg
     */
    updateUserName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof SysOrg
     */
    tenantId?: number | null;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof SysOrg
     */
    pid?: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof SysOrg
     */
    name: string;

    /**
     * 编码
     *
     * @type {string}
     * @memberof SysOrg
     */
    code?: string | null;

    /**
     * 级别
     *
     * @type {number}
     * @memberof SysOrg
     */
    level?: number | null;

    /**
     * 机构类型-数据字典
     *
     * @type {string}
     * @memberof SysOrg
     */
    type?: string | null;

    /**
     * 负责人Id
     *
     * @type {number}
     * @memberof SysOrg
     */
    directorId?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof SysOrg
     */
    orderNo?: number;

    /**
     * @type {StatusEnum}
     * @memberof SysOrg
     */
    status?: StatusEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof SysOrg
     */
    remark?: string | null;

    /**
     * 机构子项
     *
     * @type {Array<SysOrg>}
     * @memberof SysOrg
     */
    children?: Array<SysOrg> | null;

    /**
     * 是否禁止选中
     *
     * @type {boolean}
     * @memberof SysOrg
     */
    disabled?: boolean;
}
