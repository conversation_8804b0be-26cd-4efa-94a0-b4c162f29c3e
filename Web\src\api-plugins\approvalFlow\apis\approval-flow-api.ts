/* tslint:disable */
/* eslint-disable */
/**
 * ApprovalFlow
 * <br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AddApprovalFlowInput } from '../models';
import { AdminResultApprovalFlow } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListApprovalFlowOutput } from '../models';
import { AdminResultSqlSugarPagedListApprovalFlowOutput } from '../models';
import { ApprovalFlowInput } from '../models';
import { BaseIdInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
import { UpdateApprovalFlowInput } from '../models';
/**
 * ApprovalFlowApi - axios parameter creator
 * @export
 */
export const ApprovalFlowApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加审批流
         * @param {AddApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiApprovalFlowAddPost: async (body?: AddApprovalFlowInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/approvalFlow/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除审批流
         * @param {BaseIdInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiApprovalFlowDeletePost: async (body?: BaseIdInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/approvalFlow/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取审批流
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiApprovalFlowDetailGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiApprovalFlowDetailGet.');
            }
            const localVarPath = `/api/approvalFlow/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据编码获取审批流信息
         * @param {string} [code] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiApprovalFlowInfoGet: async (code?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/approvalFlow/info`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (code !== undefined) {
                localVarQueryParameter['code'] = code;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取审批流列表
         * @param {string} [code] 编号
         * @param {string} [name] 名称
         * @param {string} [remark] 备注
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiApprovalFlowListGet: async (code?: string, name?: string, remark?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/approvalFlow/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (code !== undefined) {
                localVarQueryParameter['Code'] = code;
            }

            if (name !== undefined) {
                localVarQueryParameter['Name'] = name;
            }

            if (remark !== undefined) {
                localVarQueryParameter['Remark'] = remark;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询审批流
         * @param {ApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiApprovalFlowPagePost: async (body?: ApprovalFlowInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/approvalFlow/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新审批流
         * @param {UpdateApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiApprovalFlowUpdatePost: async (body?: UpdateApprovalFlowInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/approvalFlow/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ApprovalFlowApi - functional programming interface
 * @export
 */
export const ApprovalFlowApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加审批流
         * @param {AddApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowAddPost(body?: AddApprovalFlowInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await ApprovalFlowApiAxiosParamCreator(configuration).apiApprovalFlowAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除审批流
         * @param {BaseIdInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowDeletePost(body?: BaseIdInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ApprovalFlowApiAxiosParamCreator(configuration).apiApprovalFlowDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取审批流
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowDetailGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultApprovalFlow>>> {
            const localVarAxiosArgs = await ApprovalFlowApiAxiosParamCreator(configuration).apiApprovalFlowDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据编码获取审批流信息
         * @param {string} [code] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowInfoGet(code?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultApprovalFlow>>> {
            const localVarAxiosArgs = await ApprovalFlowApiAxiosParamCreator(configuration).apiApprovalFlowInfoGet(code, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取审批流列表
         * @param {string} [code] 编号
         * @param {string} [name] 名称
         * @param {string} [remark] 备注
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowListGet(code?: string, name?: string, remark?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListApprovalFlowOutput>>> {
            const localVarAxiosArgs = await ApprovalFlowApiAxiosParamCreator(configuration).apiApprovalFlowListGet(code, name, remark, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询审批流
         * @param {ApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowPagePost(body?: ApprovalFlowInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListApprovalFlowOutput>>> {
            const localVarAxiosArgs = await ApprovalFlowApiAxiosParamCreator(configuration).apiApprovalFlowPagePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新审批流
         * @param {UpdateApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowUpdatePost(body?: UpdateApprovalFlowInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ApprovalFlowApiAxiosParamCreator(configuration).apiApprovalFlowUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * ApprovalFlowApi - factory interface
 * @export
 */
export const ApprovalFlowApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加审批流
         * @param {AddApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowAddPost(body?: AddApprovalFlowInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return ApprovalFlowApiFp(configuration).apiApprovalFlowAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除审批流
         * @param {BaseIdInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowDeletePost(body?: BaseIdInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ApprovalFlowApiFp(configuration).apiApprovalFlowDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取审批流
         * @param {number} id 主键Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowDetailGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultApprovalFlow>> {
            return ApprovalFlowApiFp(configuration).apiApprovalFlowDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据编码获取审批流信息
         * @param {string} [code] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowInfoGet(code?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultApprovalFlow>> {
            return ApprovalFlowApiFp(configuration).apiApprovalFlowInfoGet(code, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取审批流列表
         * @param {string} [code] 编号
         * @param {string} [name] 名称
         * @param {string} [remark] 备注
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowListGet(code?: string, name?: string, remark?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListApprovalFlowOutput>> {
            return ApprovalFlowApiFp(configuration).apiApprovalFlowListGet(code, name, remark, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询审批流
         * @param {ApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowPagePost(body?: ApprovalFlowInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListApprovalFlowOutput>> {
            return ApprovalFlowApiFp(configuration).apiApprovalFlowPagePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新审批流
         * @param {UpdateApprovalFlowInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiApprovalFlowUpdatePost(body?: UpdateApprovalFlowInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ApprovalFlowApiFp(configuration).apiApprovalFlowUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ApprovalFlowApi - object-oriented interface
 * @export
 * @class ApprovalFlowApi
 * @extends {BaseAPI}
 */
export class ApprovalFlowApi extends BaseAPI {
    /**
     * 
     * @summary 增加审批流
     * @param {AddApprovalFlowInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApprovalFlowApi
     */
    public async apiApprovalFlowAddPost(body?: AddApprovalFlowInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return ApprovalFlowApiFp(this.configuration).apiApprovalFlowAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除审批流
     * @param {BaseIdInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApprovalFlowApi
     */
    public async apiApprovalFlowDeletePost(body?: BaseIdInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ApprovalFlowApiFp(this.configuration).apiApprovalFlowDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取审批流
     * @param {number} id 主键Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApprovalFlowApi
     */
    public async apiApprovalFlowDetailGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultApprovalFlow>> {
        return ApprovalFlowApiFp(this.configuration).apiApprovalFlowDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据编码获取审批流信息
     * @param {string} [code] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApprovalFlowApi
     */
    public async apiApprovalFlowInfoGet(code?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultApprovalFlow>> {
        return ApprovalFlowApiFp(this.configuration).apiApprovalFlowInfoGet(code, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取审批流列表
     * @param {string} [code] 编号
     * @param {string} [name] 名称
     * @param {string} [remark] 备注
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApprovalFlowApi
     */
    public async apiApprovalFlowListGet(code?: string, name?: string, remark?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListApprovalFlowOutput>> {
        return ApprovalFlowApiFp(this.configuration).apiApprovalFlowListGet(code, name, remark, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询审批流
     * @param {ApprovalFlowInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApprovalFlowApi
     */
    public async apiApprovalFlowPagePost(body?: ApprovalFlowInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListApprovalFlowOutput>> {
        return ApprovalFlowApiFp(this.configuration).apiApprovalFlowPagePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新审批流
     * @param {UpdateApprovalFlowInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApprovalFlowApi
     */
    public async apiApprovalFlowUpdatePost(body?: UpdateApprovalFlowInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ApprovalFlowApiFp(this.configuration).apiApprovalFlowUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
