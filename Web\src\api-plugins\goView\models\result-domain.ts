/* tslint:disable */
/* eslint-disable */
/**
 * DingTalk
 * 集成钉钉开放平台<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { FieldDataDomain } from './field-data-domain';
/**
 * 
 * @export
 * @interface ResultDomain
 */
export interface ResultDomain {
    /**
     * 企业的corpId
     * @type {string}
     * @memberof ResultDomain
     */
    corpId?: string | null;
    /**
     * 员工的userId
     * @type {string}
     * @memberof ResultDomain
     */
    userId?: string | null;
    /**
     * 暂未开放
     * @type {string}
     * @memberof ResultDomain
     */
    unionId?: string | null;
    /**
     * 返回的字段信息列表
     * @type {Array<FieldDataDomain>}
     * @memberof ResultDomain
     */
    fieldDataList?: Array<FieldDataDomain> | null;
}
