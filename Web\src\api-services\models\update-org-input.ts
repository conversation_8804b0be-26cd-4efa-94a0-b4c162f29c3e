/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
import { SysOrg } from './sys-org';
 /**
 * 
 *
 * @export
 * @interface UpdateOrgInput
 */
export interface UpdateOrgInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateOrgInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateOrgInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateOrgInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateOrgInput
     */
    updateUserName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    tenantId?: number | null;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    pid?: number;

    /**
     * 编码
     *
     * @type {string}
     * @memberof UpdateOrgInput
     */
    code?: string | null;

    /**
     * 级别
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    level?: number | null;

    /**
     * 负责人Id
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    directorId?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateOrgInput
     */
    orderNo?: number;

    /**
     * @type {StatusEnum}
     * @memberof UpdateOrgInput
     */
    status?: StatusEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateOrgInput
     */
    remark?: string | null;

    /**
     * 机构子项
     *
     * @type {Array<SysOrg>}
     * @memberof UpdateOrgInput
     */
    children?: Array<SysOrg> | null;

    /**
     * 是否禁止选中
     *
     * @type {boolean}
     * @memberof UpdateOrgInput
     */
    disabled?: boolean;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateOrgInput
     */
    name: string;

    /**
     * 机构类型
     *
     * @type {string}
     * @memberof UpdateOrgInput
     */
    type?: string | null;
}
